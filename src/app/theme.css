:root {
  /* Modern logo-inspired color scheme */
  /* Blue gradient start: #2196F3 -> oklch(0.67 0.16 245) */
  /* Purple gradient end: #673AB7 -> oklch(0.45 0.18 285) */
  /* Lime green accent: #8BC34A -> oklch(0.75 0.15 125) */

  --background: oklch(1 0 0);
  --foreground: oklch(0.15 0.02 285);
  --card: oklch(0.98 0.01 245);
  --card-foreground: oklch(0.15 0.02 285);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.15 0.02 285);
  --primary: oklch(0.67 0.16 245); /* Logo blue */
  --primary-foreground: oklch(1 0 0);
  --secondary: oklch(0.45 0.18 285); /* Logo purple */
  --secondary-foreground: oklch(1 0 0);
  --muted: oklch(0.95 0.01 245);
  --muted-foreground: oklch(0.45 0.05 285);
  --accent: oklch(0.75 0.15 125); /* Logo lime green */
  --accent-foreground: oklch(0.15 0.02 285);
  --destructive: oklch(0.62 0.24 25.77);
  --destructive-foreground: oklch(1 0 0);
  --border: oklch(0.92 0.02 245);
  --input: oklch(0.98 0.01 245);
  --ring: oklch(0.67 0.16 245);
  --chart-1: oklch(0.67 0.16 245); /* Blue */
  --chart-2: oklch(0.45 0.18 285); /* Purple */
  --chart-3: oklch(0.75 0.15 125); /* Lime green */
  --chart-4: oklch(0.55 0.17 265); /* Blue-purple blend */
  --chart-5: oklch(0.65 0.12 85); /* Yellow-green */
  --sidebar: oklch(0.98 0.01 245);
  --sidebar-foreground: oklch(0.15 0.02 285);
  --sidebar-primary: oklch(0.67 0.16 245);
  --sidebar-primary-foreground: oklch(1 0 0);
  --sidebar-accent: oklch(0.75 0.15 125);
  --sidebar-accent-foreground: oklch(0.15 0.02 285);
  --sidebar-border: oklch(0.92 0.02 245);
  --sidebar-ring: oklch(0.67 0.16 245);
  --font-sans: Open Sans, sans-serif;
  --font-serif: Georgia, serif;
  --font-mono: Menlo, monospace;
  --radius: 1.3rem;
  --shadow-2xs: 0px 2px 0px 0px hsl(202.82 89.12% 53.14% / 0);
  --shadow-xs: 0px 2px 0px 0px hsl(202.82 89.12% 53.14% / 0);
  --shadow-sm: 0px 2px 0px 0px hsl(202.82 89.12% 53.14% / 0),
    0px 1px 2px -1px hsl(202.82 89.12% 53.14% / 0);
  --shadow: 0px 2px 0px 0px hsl(202.82 89.12% 53.14% / 0),
    0px 1px 2px -1px hsl(202.82 89.12% 53.14% / 0);
  --shadow-md: 0px 2px 0px 0px hsl(202.82 89.12% 53.14% / 0),
    0px 2px 4px -1px hsl(202.82 89.12% 53.14% / 0);
  --shadow-lg: 0px 2px 0px 0px hsl(202.82 89.12% 53.14% / 0),
    0px 4px 6px -1px hsl(202.82 89.12% 53.14% / 0);
  --shadow-xl: 0px 2px 0px 0px hsl(202.82 89.12% 53.14% / 0),
    0px 8px 10px -1px hsl(202.82 89.12% 53.14% / 0);
  --shadow-2xl: 0px 2px 0px 0px hsl(202.82 89.12% 53.14% / 0);

  /* Modern gradient variables inspired by logo */
  --gradient-primary: linear-gradient(135deg, #2196f3 0%, #673ab7 100%);
  --gradient-primary-soft: linear-gradient(
    135deg,
    rgba(33, 150, 243, 0.1) 0%,
    rgba(103, 58, 183, 0.1) 100%
  );
  --gradient-accent: linear-gradient(135deg, #8bc34a 0%, #4caf50 100%);
  --gradient-card: linear-gradient(
    135deg,
    rgba(33, 150, 243, 0.05) 0%,
    rgba(103, 58, 183, 0.05) 100%
  );
}

.dark {
  /* Dark theme with logo-inspired colors */
  --background: oklch(0.08 0.01 285);
  --foreground: oklch(0.95 0.01 245);
  --card: oklch(0.12 0.02 285);
  --card-foreground: oklch(0.95 0.01 245);
  --popover: oklch(0.08 0.01 285);
  --popover-foreground: oklch(0.95 0.01 245);
  --primary: oklch(0.72 0.18 245); /* Brighter blue for dark mode */
  --primary-foreground: oklch(0.08 0.01 285);
  --secondary: oklch(0.55 0.2 285); /* Brighter purple for dark mode */
  --secondary-foreground: oklch(0.95 0.01 245);
  --muted: oklch(0.15 0.02 285);
  --muted-foreground: oklch(0.65 0.05 245);
  --accent: oklch(0.8 0.18 125); /* Brighter lime green for dark mode */
  --accent-foreground: oklch(0.08 0.01 285);
  --destructive: oklch(0.62 0.24 25.77);
  --destructive-foreground: oklch(1 0 0);
  --border: oklch(0.25 0.03 285);
  --input: oklch(0.18 0.03 285);
  --ring: oklch(0.72 0.18 245);
  --chart-1: oklch(0.72 0.18 245); /* Blue */
  --chart-2: oklch(0.55 0.2 285); /* Purple */
  --chart-3: oklch(0.8 0.18 125); /* Lime green */
  --chart-4: oklch(0.65 0.19 265); /* Blue-purple blend */
  --chart-5: oklch(0.7 0.15 85); /* Yellow-green */
  --sidebar: oklch(0.12 0.02 285);
  --sidebar-foreground: oklch(0.95 0.01 245);
  --sidebar-primary: oklch(0.72 0.18 245);
  --sidebar-primary-foreground: oklch(0.08 0.01 285);
  --sidebar-accent: oklch(0.8 0.18 125);
  --sidebar-accent-foreground: oklch(0.08 0.01 285);
  --sidebar-border: oklch(0.25 0.03 285);
  --sidebar-ring: oklch(0.72 0.18 245);
  --font-sans: Open Sans, sans-serif;
  --font-serif: Georgia, serif;
  --font-mono: Menlo, monospace;
  --radius: 1.3rem;
  --shadow-2xs: 0px 2px 0px 0px hsl(202.82 89.12% 53.14% / 0);
  --shadow-xs: 0px 2px 0px 0px hsl(202.82 89.12% 53.14% / 0);
  --shadow-sm: 0px 2px 0px 0px hsl(202.82 89.12% 53.14% / 0),
    0px 1px 2px -1px hsl(202.82 89.12% 53.14% / 0);
  --shadow: 0px 2px 0px 0px hsl(202.82 89.12% 53.14% / 0),
    0px 1px 2px -1px hsl(202.82 89.12% 53.14% / 0);
  --shadow-md: 0px 2px 0px 0px hsl(202.82 89.12% 53.14% / 0),
    0px 2px 4px -1px hsl(202.82 89.12% 53.14% / 0);
  --shadow-lg: 0px 2px 0px 0px hsl(202.82 89.12% 53.14% / 0),
    0px 4px 6px -1px hsl(202.82 89.12% 53.14% / 0);
  --shadow-xl: 0px 2px 0px 0px hsl(202.82 89.12% 53.14% / 0),
    0px 8px 10px -1px hsl(202.82 89.12% 53.14% / 0);
  --shadow-2xl: 0px 2px 0px 0px hsl(202.82 89.12% 53.14% / 0);

  /* Dark theme gradients */
  --gradient-primary: linear-gradient(135deg, #42a5f5 0%, #7e57c2 100%);
  --gradient-primary-soft: linear-gradient(
    135deg,
    rgba(66, 165, 245, 0.15) 0%,
    rgba(126, 87, 194, 0.15) 100%
  );
  --gradient-accent: linear-gradient(135deg, #9ccc65 0%, #66bb6a 100%);
  --gradient-card: linear-gradient(
    135deg,
    rgba(66, 165, 245, 0.08) 0%,
    rgba(126, 87, 194, 0.08) 100%
  );
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);

  --font-sans: var(--font-sans);
  --font-mono: var(--font-mono);
  --font-serif: var(--font-serif);

  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);

  --shadow-2xs: var(--shadow-2xs);
  --shadow-xs: var(--shadow-xs);
  --shadow-sm: var(--shadow-sm);
  --shadow: var(--shadow);
  --shadow-md: var(--shadow-md);
  --shadow-lg: var(--shadow-lg);
  --shadow-xl: var(--shadow-xl);
  --shadow-2xl: var(--shadow-2xl);
}
