:root {
  /* Warm yellow-toned color scheme */
  /* Primary golden yellow: #F9A825 -> oklch(0.75 0.15 85) */
  /* Secondary amber: #FF8F00 -> oklch(0.70 0.18 65) */
  /* Accent warm orange: #FF6F00 -> oklch(0.65 0.20 45) */

  --background: oklch(1 0 0);
  --foreground: oklch(0.2 0.02 45);
  --card: oklch(0.98 0.01 85);
  --card-foreground: oklch(0.2 0.02 45);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.2 0.02 45);
  --primary: oklch(0.75 0.15 85); /* Golden yellow */
  --primary-foreground: oklch(0.15 0.02 45);
  --secondary: oklch(0.7 0.18 65); /* Amber */
  --secondary-foreground: oklch(0.15 0.02 45);
  --muted: oklch(0.96 0.02 85);
  --muted-foreground: oklch(0.5 0.05 65);
  --accent: oklch(0.65 0.2 45); /* Warm orange */
  --accent-foreground: oklch(1 0 0);
  --destructive: oklch(0.62 0.24 25.77);
  --destructive-foreground: oklch(1 0 0);
  --border: oklch(0.93 0.03 85);
  --input: oklch(0.98 0.02 85);
  --ring: oklch(0.75 0.15 85);
  --chart-1: oklch(0.75 0.15 85); /* Golden yellow */
  --chart-2: oklch(0.7 0.18 65); /* Amber */
  --chart-3: oklch(0.65 0.2 45); /* Warm orange */
  --chart-4: oklch(0.8 0.12 95); /* Light yellow */
  --chart-5: oklch(0.6 0.22 35); /* Deep orange */
  --sidebar: oklch(0.98 0.01 85);
  --sidebar-foreground: oklch(0.2 0.02 45);
  --sidebar-primary: oklch(0.75 0.15 85);
  --sidebar-primary-foreground: oklch(0.15 0.02 45);
  --sidebar-accent: oklch(0.65 0.2 45);
  --sidebar-accent-foreground: oklch(1 0 0);
  --sidebar-border: oklch(0.93 0.03 85);
  --sidebar-ring: oklch(0.75 0.15 85);
  --font-sans: Open Sans, sans-serif;
  --font-serif: Georgia, serif;
  --font-mono: Menlo, monospace;
  --radius: 1.3rem;
  --shadow-2xs: 0px 2px 0px 0px hsl(202.82 89.12% 53.14% / 0);
  --shadow-xs: 0px 2px 0px 0px hsl(202.82 89.12% 53.14% / 0);
  --shadow-sm: 0px 2px 0px 0px hsl(202.82 89.12% 53.14% / 0),
    0px 1px 2px -1px hsl(202.82 89.12% 53.14% / 0);
  --shadow: 0px 2px 0px 0px hsl(202.82 89.12% 53.14% / 0),
    0px 1px 2px -1px hsl(202.82 89.12% 53.14% / 0);
  --shadow-md: 0px 2px 0px 0px hsl(202.82 89.12% 53.14% / 0),
    0px 2px 4px -1px hsl(202.82 89.12% 53.14% / 0);
  --shadow-lg: 0px 2px 0px 0px hsl(202.82 89.12% 53.14% / 0),
    0px 4px 6px -1px hsl(202.82 89.12% 53.14% / 0);
  --shadow-xl: 0px 2px 0px 0px hsl(202.82 89.12% 53.14% / 0),
    0px 8px 10px -1px hsl(202.82 89.12% 53.14% / 0);
  --shadow-2xl: 0px 2px 0px 0px hsl(202.82 89.12% 53.14% / 0);

  /* Warm yellow gradient variables */
  --gradient-primary: linear-gradient(135deg, #f9a825 0%, #ff8f00 100%);
  --gradient-primary-soft: linear-gradient(
    135deg,
    rgba(249, 168, 37, 0.1) 0%,
    rgba(255, 143, 0, 0.1) 100%
  );
  --gradient-accent: linear-gradient(135deg, #ff6f00 0%, #e65100 100%);
  --gradient-card: linear-gradient(
    135deg,
    rgba(249, 168, 37, 0.05) 0%,
    rgba(255, 143, 0, 0.05) 100%
  );
}

.dark {
  /* Dark theme with warm yellow tones */
  --background: oklch(0.1 0.02 45);
  --foreground: oklch(0.95 0.02 85);
  --card: oklch(0.15 0.03 45);
  --card-foreground: oklch(0.95 0.02 85);
  --popover: oklch(0.1 0.02 45);
  --popover-foreground: oklch(0.95 0.02 85);
  --primary: oklch(0.8 0.18 85); /* Brighter golden yellow for dark mode */
  --primary-foreground: oklch(0.1 0.02 45);
  --secondary: oklch(0.75 0.2 65); /* Brighter amber for dark mode */
  --secondary-foreground: oklch(0.1 0.02 45);
  --muted: oklch(0.2 0.03 45);
  --muted-foreground: oklch(0.7 0.05 85);
  --accent: oklch(0.7 0.22 45); /* Brighter warm orange for dark mode */
  --accent-foreground: oklch(0.95 0.02 85);
  --destructive: oklch(0.62 0.24 25.77);
  --destructive-foreground: oklch(1 0 0);
  --border: oklch(0.3 0.05 45);
  --input: oklch(0.25 0.04 45);
  --ring: oklch(0.8 0.18 85);
  --chart-1: oklch(0.8 0.18 85); /* Golden yellow */
  --chart-2: oklch(0.75 0.2 65); /* Amber */
  --chart-3: oklch(0.7 0.22 45); /* Warm orange */
  --chart-4: oklch(0.85 0.15 95); /* Light yellow */
  --chart-5: oklch(0.65 0.24 35); /* Deep orange */
  --sidebar: oklch(0.15 0.03 45);
  --sidebar-foreground: oklch(0.95 0.02 85);
  --sidebar-primary: oklch(0.8 0.18 85);
  --sidebar-primary-foreground: oklch(0.1 0.02 45);
  --sidebar-accent: oklch(0.7 0.22 45);
  --sidebar-accent-foreground: oklch(0.95 0.02 85);
  --sidebar-border: oklch(0.3 0.05 45);
  --sidebar-ring: oklch(0.8 0.18 85);
  --font-sans: Open Sans, sans-serif;
  --font-serif: Georgia, serif;
  --font-mono: Menlo, monospace;
  --radius: 1.3rem;
  --shadow-2xs: 0px 2px 0px 0px hsl(202.82 89.12% 53.14% / 0);
  --shadow-xs: 0px 2px 0px 0px hsl(202.82 89.12% 53.14% / 0);
  --shadow-sm: 0px 2px 0px 0px hsl(202.82 89.12% 53.14% / 0),
    0px 1px 2px -1px hsl(202.82 89.12% 53.14% / 0);
  --shadow: 0px 2px 0px 0px hsl(202.82 89.12% 53.14% / 0),
    0px 1px 2px -1px hsl(202.82 89.12% 53.14% / 0);
  --shadow-md: 0px 2px 0px 0px hsl(202.82 89.12% 53.14% / 0),
    0px 2px 4px -1px hsl(202.82 89.12% 53.14% / 0);
  --shadow-lg: 0px 2px 0px 0px hsl(202.82 89.12% 53.14% / 0),
    0px 4px 6px -1px hsl(202.82 89.12% 53.14% / 0);
  --shadow-xl: 0px 2px 0px 0px hsl(202.82 89.12% 53.14% / 0),
    0px 8px 10px -1px hsl(202.82 89.12% 53.14% / 0);
  --shadow-2xl: 0px 2px 0px 0px hsl(202.82 89.12% 53.14% / 0);

  /* Dark theme yellow gradients */
  --gradient-primary: linear-gradient(135deg, #ffb74d 0%, #ffa726 100%);
  --gradient-primary-soft: linear-gradient(
    135deg,
    rgba(255, 183, 77, 0.15) 0%,
    rgba(255, 167, 38, 0.15) 100%
  );
  --gradient-accent: linear-gradient(135deg, #ff8a65 0%, #ff7043 100%);
  --gradient-card: linear-gradient(
    135deg,
    rgba(255, 183, 77, 0.08) 0%,
    rgba(255, 167, 38, 0.08) 100%
  );
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);

  --font-sans: var(--font-sans);
  --font-mono: var(--font-mono);
  --font-serif: var(--font-serif);

  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);

  --shadow-2xs: var(--shadow-2xs);
  --shadow-xs: var(--shadow-xs);
  --shadow-sm: var(--shadow-sm);
  --shadow: var(--shadow);
  --shadow-md: var(--shadow-md);
  --shadow-lg: var(--shadow-lg);
  --shadow-xl: var(--shadow-xl);
  --shadow-2xl: var(--shadow-2xl);
}

/* Modern gradient utility classes */
.gradient-primary {
  background: var(--gradient-primary);
}

.gradient-primary-soft {
  background: var(--gradient-primary-soft);
}

.gradient-accent {
  background: var(--gradient-accent);
}

.gradient-card {
  background: var(--gradient-card);
}

/* Modern button styles with gradients */
.btn-gradient {
  background: var(--gradient-primary);
  border: none;
  color: white;
  transition: all 0.3s ease;
}

.btn-gradient:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(249, 168, 37, 0.3);
}

/* Modern card with subtle gradient */
.card-gradient {
  background: var(--gradient-card);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Accent elements */
.accent-dot {
  background: var(--accent);
  border-radius: 50%;
}

.text-gradient {
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}
