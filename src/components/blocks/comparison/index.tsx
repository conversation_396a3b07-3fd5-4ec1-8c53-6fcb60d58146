'use client'

import React from 'react'
import { motion } from 'framer-motion'
import { RiCheckLine, RiCloseLine, RiStarFill, RiScalesLine } from 'react-icons/ri'

interface ComparisonProps {
  data: {
    name: string
    title: string
    description: string
    subtitle: string
    columns: string[]
    options: Array<{
      method: string
      time: string
      cost: string
      success_rate: string
      risk: string
      highlight: boolean
      badge?: string
      description: string
    }>
    cta: {
      title: string
      url: string
    }
  }
}

export default function Comparison({ data }: ComparisonProps) {
  const getRiskColor = (risk: string) => {
    switch (risk.toLowerCase()) {
      case 'very high':
        return 'bg-red-100 text-red-700 dark:bg-red-900/30 dark:text-red-300'
      case 'high':
        return 'bg-orange-100 text-orange-700 dark:bg-orange-900/30 dark:text-orange-300'
      case 'minimal':
        return 'bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-300'
      default:
        return 'bg-gray-100 text-gray-700 dark:bg-gray-900/30 dark:text-gray-300'
    }
  }

  const getSuccessRateColor = (rate: string) => {
    const percentage = parseInt(rate)
    if (percentage >= 80) return 'text-green-600 dark:text-green-400'
    if (percentage >= 50) return 'text-yellow-600 dark:text-yellow-400'
    return 'text-red-600 dark:text-red-400'
  }

  return (
    <section className="py-20 bg-gradient-to-br from-blue-50 to-purple-50 dark:from-gray-900 dark:to-gray-800">
      <div className="container mx-auto px-4">
        {/* Header */}
        <div className="text-center mb-16">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="inline-flex items-center gap-2 px-4 py-2 bg-blue-100 dark:bg-blue-900/30 rounded-full text-blue-600 dark:text-blue-400 text-sm font-medium mb-4"
          >
            <RiScalesLine className="w-4 h-4" />
            Smart Comparison
          </motion.div>
          
          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.1 }}
            className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-4"
          >
            {data.title}
          </motion.h2>
          
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="text-lg text-gray-600 dark:text-gray-300 mb-2"
          >
            {data.description}
          </motion.p>
          
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
            className="text-sm text-gray-500 dark:text-gray-400"
          >
            {data.subtitle}
          </motion.p>
        </div>

        {/* Comparison Table */}
        <div className="max-w-6xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
            className="bg-white dark:bg-gray-800 rounded-2xl shadow-2xl overflow-hidden"
          >
            {/* Table Header */}
            <div className="bg-gray-50 dark:bg-gray-700 px-6 py-4">
              <div className="grid grid-cols-6 gap-4 text-sm font-semibold text-gray-700 dark:text-gray-300">
                <div className="col-span-2">Method</div>
                <div className="text-center">Time</div>
                <div className="text-center">Cost</div>
                <div className="text-center">Success Rate</div>
                <div className="text-center">Risk</div>
              </div>
            </div>

            {/* Table Body */}
            <div className="divide-y divide-gray-200 dark:divide-gray-700">
              {data.options.map((option, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, x: -20 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.6, delay: 0.5 + index * 0.1 }}
                  className={`relative px-6 py-6 ${
                    option.highlight 
                      ? 'bg-green-50 dark:bg-green-900/10 border-l-4 border-green-500' 
                      : 'bg-white dark:bg-gray-800'
                  }`}
                >
                  {/* Badge */}
                  {option.badge && (
                    <div className="absolute top-4 right-4 bg-green-500 text-white px-3 py-1 rounded-full text-xs font-semibold">
                      {option.badge}
                    </div>
                  )}

                  <div className="grid grid-cols-6 gap-4 items-center">
                    {/* Method */}
                    <div className="col-span-2">
                      <div className="flex items-center gap-3">
                        <div className={`w-3 h-3 rounded-full ${
                          option.highlight ? 'bg-green-500' : 'bg-gray-300'
                        }`} />
                        <div>
                          <h3 className="font-semibold text-gray-900 dark:text-white mb-1">
                            {option.method}
                          </h3>
                          <p className="text-sm text-gray-500 dark:text-gray-400">
                            {option.description}
                          </p>
                        </div>
                      </div>
                    </div>

                    {/* Time */}
                    <div className="text-center">
                      <div className="font-medium text-gray-900 dark:text-white">
                        {option.time}
                      </div>
                    </div>

                    {/* Cost */}
                    <div className="text-center">
                      <div className="font-medium text-gray-900 dark:text-white">
                        {option.cost}
                      </div>
                    </div>

                    {/* Success Rate */}
                    <div className="text-center">
                      <div className={`font-bold text-lg ${getSuccessRateColor(option.success_rate)}`}>
                        {option.success_rate}
                      </div>
                    </div>

                    {/* Risk */}
                    <div className="text-center">
                      <span className={`px-3 py-1 rounded-full text-sm font-medium ${getRiskColor(option.risk)}`}>
                        {option.risk}
                      </span>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          </motion.div>

          {/* Benefits Summary */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.8 }}
            className="mt-12 grid md:grid-cols-3 gap-6"
          >
            <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg">
              <div className="flex items-center gap-3 mb-4">
                <div className="w-10 h-10 bg-green-100 dark:bg-green-900/30 rounded-lg flex items-center justify-center">
                  <RiCheckLine className="w-5 h-5 text-green-600 dark:text-green-400" />
                </div>
                <h3 className="font-semibold text-gray-900 dark:text-white">167x Faster</h3>
              </div>
              <p className="text-sm text-gray-600 dark:text-gray-300">
                Launch in 3-7 days instead of 3-6 months
              </p>
            </div>

            <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg">
              <div className="flex items-center gap-3 mb-4">
                <div className="w-10 h-10 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center">
                  <RiCheckLine className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                </div>
                <h3 className="font-semibold text-gray-900 dark:text-white">168x Cheaper</h3>
              </div>
              <p className="text-sm text-gray-600 dark:text-gray-300">
                Pay $297 instead of $50,000+
              </p>
            </div>

            <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg">
              <div className="flex items-center gap-3 mb-4">
                <div className="w-10 h-10 bg-purple-100 dark:bg-purple-900/30 rounded-lg flex items-center justify-center">
                  <RiStarFill className="w-5 h-5 text-purple-600 dark:text-purple-400" />
                </div>
                <h3 className="font-semibold text-gray-900 dark:text-white">8.5x Success Rate</h3>
              </div>
              <p className="text-sm text-gray-600 dark:text-gray-300">
                85% success rate vs 10% traditional
              </p>
            </div>
          </motion.div>

          {/* CTA */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.9 }}
            className="text-center mt-12"
          >
            <div className="bg-gradient-to-r from-green-500 to-blue-600 rounded-2xl p-8 text-white">
              <h3 className="text-2xl font-bold mb-4">Ready to make the smart choice?</h3>
              <p className="text-lg mb-6 opacity-90">
                Join the developers who chose speed, savings, and success
              </p>
              <a
                href={data.cta.url}
                className="inline-flex items-center gap-2 bg-white text-green-600 px-8 py-4 rounded-lg font-semibold hover:bg-gray-100 transition-colors"
              >
                {data.cta.title}
              </a>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  )
}