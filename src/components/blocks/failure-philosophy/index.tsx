'use client'

import React from 'react'
import { motion } from 'framer-motion'
import { RiD<PERSON><PERSON>ine, RiMoneyDollarCircleLine, RiLineChartLine, RiFlashlightLine, RiLightbulbLine } from 'react-icons/ri'

interface FailurePhilosophyProps {
  data: {
    name: string
    title: string
    subtitle: string
    description: string
    advantages: Array<{
      icon: string
      title: string
      description: string
      stat: string
    }>
    cta: {
      title: string
      url: string
    }
  }
}

export default function FailurePhilosophy({ data }: FailurePhilosophyProps) {
  const getIcon = (iconName: string) => {
    switch (iconName) {
      case 'RiDiceLine':
        return <RiDiceLine className="w-6 h-6" />
      case 'RiMoneyDollarCircleLine':
        return <RiMoneyDollarCircleLine className="w-6 h-6" />
      case 'RiLineChartLine':
        return <RiLineChartLine className="w-6 h-6" />
      case 'RiFlashlightLine':
        return <RiFlashlightLine className="w-6 h-6" />
      default:
        return <RiLightbulbLine className="w-6 h-6" />
    }
  }

  const getIconColor = (index: number) => {
    const colors = [
      'bg-blue-100 text-blue-600 dark:bg-blue-900/30 dark:text-blue-400',
      'bg-green-100 text-green-600 dark:bg-green-900/30 dark:text-green-400',
      'bg-purple-100 text-purple-600 dark:bg-purple-900/30 dark:text-purple-400',
      'bg-orange-100 text-orange-600 dark:bg-orange-900/30 dark:text-orange-400'
    ]
    return colors[index % colors.length]
  }

  return (
    <section className="py-20 bg-gradient-to-br from-gray-50 to-orange-50 dark:from-gray-900 dark:to-gray-800">
      <div className="container mx-auto px-4">
        {/* Header */}
        <div className="text-center mb-16">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="inline-flex items-center gap-2 px-4 py-2 bg-orange-100 dark:bg-orange-900/30 rounded-full text-orange-600 dark:text-orange-400 text-sm font-medium mb-4"
          >
            <RiLightbulbLine className="w-4 h-4" />
            Failure Strategy
          </motion.div>
          
          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.1 }}
            className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-4"
          >
            {data.title}
          </motion.h2>
          
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="text-xl text-gray-600 dark:text-gray-300 mb-2"
          >
            {data.subtitle}
          </motion.p>
          
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
            className="text-sm text-gray-500 dark:text-gray-400"
          >
            {data.description}
          </motion.p>
        </div>

        {/* Advantages Grid */}
        <div className="max-w-6xl mx-auto">
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
            {data.advantages.map((advantage, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className="bg-white dark:bg-gray-800 rounded-2xl p-8 shadow-xl hover:shadow-2xl transition-all duration-300 group"
              >
                <div className="text-center">
                  {/* Icon */}
                  <div className={`w-16 h-16 ${getIconColor(index)} rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform`}>
                    {getIcon(advantage.icon)}
                  </div>
                  
                  {/* Title */}
                  <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-4">
                    {advantage.title}
                  </h3>
                  
                  {/* Description */}
                  <p className="text-gray-600 dark:text-gray-300 mb-6 leading-relaxed">
                    {advantage.description}
                  </p>
                  
                  {/* Stat */}
                  <div className="bg-gradient-to-r from-orange-100 to-red-100 dark:from-orange-900/30 dark:to-red-900/30 rounded-lg p-4 border border-orange-200 dark:border-orange-800">
                    <div className="text-orange-700 dark:text-orange-300 font-bold text-lg">
                      {advantage.stat}
                    </div>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>

          {/* Philosophy Statement */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
            className="bg-white dark:bg-gray-800 rounded-2xl p-8 shadow-xl mb-12"
          >
            <div className="text-center max-w-3xl mx-auto">
              <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
                The AppSolve Advantage
              </h3>
              <p className="text-lg text-gray-600 dark:text-gray-300 mb-6">
                While others fear failure, you embrace it as a stepping stone to success. 
                With AppSolve, failure becomes cheap, fast, and educational. Your competition 
                is still building their first app while you're already on your third success.
              </p>
              
              <div className="grid md:grid-cols-3 gap-6 text-center">
                <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4">
                  <div className="text-2xl font-bold text-blue-600 dark:text-blue-400 mb-2">
                    $297
                  </div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">
                    Cost per attempt
                  </div>
                </div>
                
                <div className="bg-green-50 dark:bg-green-900/20 rounded-lg p-4">
                  <div className="text-2xl font-bold text-green-600 dark:text-green-400 mb-2">
                    3-7 days
                  </div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">
                    Time to validate
                  </div>
                </div>
                
                <div className="bg-purple-50 dark:bg-purple-900/20 rounded-lg p-4">
                  <div className="text-2xl font-bold text-purple-600 dark:text-purple-400 mb-2">
                    85%
                  </div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">
                    Success rate by app #3
                  </div>
                </div>
              </div>
            </div>
          </motion.div>

          {/* CTA */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.5 }}
            className="text-center"
          >
            <div className="bg-gradient-to-r from-orange-500 to-red-500 rounded-2xl p-8 text-white">
              <h3 className="text-2xl font-bold mb-4">Ready to turn failure into fuel?</h3>
              <p className="text-lg mb-6 opacity-90">
                Start building your portfolio of successful apps today
              </p>
              <a
                href={data.cta.url}
                className="inline-flex items-center gap-2 bg-white text-orange-600 px-8 py-4 rounded-lg font-semibold hover:bg-gray-100 transition-colors"
              >
                {data.cta.title}
              </a>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  )
}