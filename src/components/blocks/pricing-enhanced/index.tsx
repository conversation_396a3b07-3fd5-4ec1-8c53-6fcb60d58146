'use client'

import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { RiCheckLine, RiFlashlightLine, RiShieldCheckLine, RiFireLine, RiEyeLine, RiShoppingCartLine } from 'react-icons/ri'

interface PricingEnhancedProps {
  data: {
    name: string
    title: string
    description: string
    highlight: string
    urgency: {
      stock_limit: {
        enabled: boolean
        current: number
        total: number
        message: string
      }
      social_proof: {
        enabled: boolean
        messages: string[]
        interval: number
      }
    }
    plans: Array<{
      name: string
      price: string
      original_price: string
      savings: string
      description: string
      badge: string
      features: string[]
      cta: string
      popular: boolean
      guarantee: {
        title: string
        description: string
      }
    }>
    social_proof: {
      title: string
      avatars: string[]
    }
  }
}

export default function PricingEnhanced({ data }: PricingEnhancedProps) {
  const [currentMessage, setCurrentMessage] = useState(0)
  const [stockLeft, setStockLeft] = useState(data.urgency.stock_limit.current)

  useEffect(() => {
    if (!data.urgency.social_proof.enabled) return

    const interval = setInterval(() => {
      setCurrentMessage((prev) => (prev + 1) % data.urgency.social_proof.messages.length)
    }, data.urgency.social_proof.interval)

    return () => clearInterval(interval)
  }, [data.urgency.social_proof.enabled, data.urgency.social_proof.interval, data.urgency.social_proof.messages.length])

  return (
    <section id="pricing" className="py-20 bg-gradient-to-br from-gray-50 to-blue-50 dark:from-gray-900 dark:to-gray-800">
      <div className="container mx-auto px-4">
        {/* Header */}
        <div className="text-center mb-16">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="inline-flex items-center gap-2 px-4 py-2 bg-red-100 dark:bg-red-900/30 rounded-full text-red-600 dark:text-red-400 text-sm font-medium mb-4"
          >
            <RiFireLine className="w-4 h-4" />
            {data.highlight}
          </motion.div>
          
          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.1 }}
            className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-4"
          >
            {data.title}
          </motion.h2>
          
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="text-lg text-gray-600 dark:text-gray-300"
          >
            {data.description}
          </motion.p>
        </div>

        {/* Urgency Elements */}
        <div className="max-w-4xl mx-auto mb-12">
          {/* Stock Limit */}
          {data.urgency.stock_limit.enabled && (
            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              whileInView={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.5, delay: 0.3 }}
              className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 mb-6"
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <RiFireLine className="w-5 h-5 text-red-500" />
                  <span className="text-red-700 dark:text-red-300 font-semibold">
                    {data.urgency.stock_limit.message}
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="flex gap-1">
                    {Array.from({ length: data.urgency.stock_limit.total }).map((_, i) => (
                      <div
                        key={i}
                        className={`w-3 h-3 rounded-full ${
                          i < stockLeft ? 'bg-red-500' : 'bg-gray-300'
                        }`}
                      />
                    ))}
                  </div>
                  <span className="text-sm text-red-600 dark:text-red-400 font-medium">
                    {stockLeft}/{data.urgency.stock_limit.total}
                  </span>
                </div>
              </div>
            </motion.div>
          )}

          {/* Social Proof Ticker */}
          {data.urgency.social_proof.enabled && (
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5, delay: 0.4 }}
              className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4 mb-6"
            >
              <div className="flex items-center gap-3">
                <RiEyeLine className="w-5 h-5 text-green-500" />
                <motion.span
                  key={currentMessage}
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -10 }}
                  className="text-green-700 dark:text-green-300 font-medium"
                >
                  {data.urgency.social_proof.messages[currentMessage]}
                </motion.span>
              </div>
            </motion.div>
          )}
        </div>

        {/* Pricing Plan */}
        <div className="max-w-lg mx-auto">
          {data.plans.map((plan, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.5 }}
              className={`relative bg-white dark:bg-gray-800 rounded-2xl shadow-2xl overflow-hidden ${
                plan.popular ? 'ring-4 ring-green-500' : ''
              }`}
            >
              {/* Badge */}
              {plan.badge && (
                <div className="absolute top-4 right-4 bg-green-500 text-white px-3 py-1 rounded-full text-sm font-semibold">
                  {plan.badge}
                </div>
              )}

              <div className="p-8">
                {/* Plan Header */}
                <div className="text-center mb-8">
                  <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                    {plan.name}
                  </h3>
                  <p className="text-gray-600 dark:text-gray-300 mb-6">
                    {plan.description}
                  </p>
                  
                  {/* Pricing */}
                  <div className="mb-6">
                    <div className="flex items-center justify-center gap-3 mb-2">
                      <span className="text-4xl font-bold text-gray-900 dark:text-white">
                        {plan.price}
                      </span>
                      <div className="text-left">
                        <div className="text-lg line-through text-gray-500">
                          {plan.original_price}
                        </div>
                        <div className="text-sm text-green-600 font-semibold">
                          {plan.savings}
                        </div>
                      </div>
                    </div>
                    <p className="text-gray-500 dark:text-gray-400 text-sm">
                      One-time payment • Lifetime access
                    </p>
                  </div>

                  {/* CTA Button */}
                  <a
                    href="#"
                    className="inline-flex items-center justify-center gap-2 w-full bg-green-600 hover:bg-green-700 text-white px-8 py-4 rounded-lg font-semibold text-lg transition-all transform hover:scale-105 mb-6"
                  >
                    <RiShoppingCartLine className="w-5 h-5" />
                    {plan.cta}
                  </a>
                </div>

                {/* Features */}
                <div className="space-y-4 mb-8">
                  {plan.features.map((feature, featureIndex) => (
                    <div key={featureIndex} className="flex items-start gap-3">
                      <RiCheckLine className="w-5 h-5 text-green-500 mt-0.5 flex-shrink-0" />
                      <span className="text-gray-700 dark:text-gray-300">
                        {feature}
                      </span>
                    </div>
                  ))}
                </div>

                {/* Guarantee */}
                <div className="bg-green-50 dark:bg-green-900/20 rounded-lg p-4 border border-green-200 dark:border-green-800">
                  <div className="flex items-start gap-3">
                    <RiShieldCheckLine className="w-5 h-5 text-green-500 mt-0.5 flex-shrink-0" />
                    <div>
                      <h4 className="font-semibold text-green-700 dark:text-green-300 mb-1">
                        {plan.guarantee.title}
                      </h4>
                      <p className="text-sm text-green-600 dark:text-green-400">
                        {plan.guarantee.description}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>
          ))}
        </div>

        {/* Social Proof */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.6 }}
          className="text-center mt-12"
        >
          <p className="text-gray-600 dark:text-gray-300 mb-4">
            {data.social_proof.title}
          </p>
          <div className="flex justify-center gap-2">
            {data.social_proof.avatars.map((avatar, index) => (
              <img
                key={index}
                src={avatar}
                alt={`User ${index + 1}`}
                className="w-10 h-10 rounded-full border-2 border-white dark:border-gray-800 shadow-lg"
              />
            ))}
          </div>
        </motion.div>
      </div>
    </section>
  )
}