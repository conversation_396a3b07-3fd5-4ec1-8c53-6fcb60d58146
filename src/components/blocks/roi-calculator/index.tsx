'use client'

import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { RiCalculatorLine, RiArrowRightLine, RiFireLine } from 'react-icons/ri'

interface ROICalculatorProps {
  data: {
    name: string
    title: string
    subtitle: string
    description: string
    inputs: Array<{
      label: string
      placeholder: string
      type: string
      min: number
      max: number
      default: number
    }>
    results: {
      monthly_revenue: string
      yearly_revenue: string
      roi_days: string
      confidence: string
    }
    cta: {
      title: string
      url: string
    }
  }
}

export default function ROICalculator({ data }: ROICalculatorProps) {
  const [users, setUsers] = useState(1000)
  const [price, setPrice] = useState(4.99)
  const [conversionRate, setConversionRate] = useState(2)
  const [results, setResults] = useState({
    monthlyRevenue: 0,
    yearlyRevenue: 0,
    roiDays: 0,
    breakeven: 0
  })

  useEffect(() => {
    const dailyRevenue = users * (conversionRate / 100) * price
    const monthlyRevenue = dailyRevenue * 30
    const yearlyRevenue = monthlyRevenue * 12
    const appSolveCost = 297
    const roiDays = Math.ceil(appSolveCost / dailyRevenue)
    const breakeven = dailyRevenue > 0 ? appSolveCost / dailyRevenue : 999

    setResults({
      monthlyRevenue,
      yearlyRevenue,
      roiDays,
      breakeven
    })
  }, [users, price, conversionRate])

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount)
  }

  return (
    <section className="py-16 bg-gradient-to-br from-green-50 to-blue-50 dark:from-gray-900 dark:to-gray-800">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="inline-flex items-center gap-2 px-4 py-2 bg-green-100 dark:bg-green-900/30 rounded-full text-green-600 dark:text-green-400 text-sm font-medium mb-4"
          >
            <RiCalculatorLine className="w-4 h-4" />
            Revenue Calculator
          </motion.div>
          
          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.1 }}
            className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4"
          >
            {data.title}
          </motion.h2>
          
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="text-lg text-gray-600 dark:text-gray-300 mb-2"
          >
            {data.subtitle}
          </motion.p>
          
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
            className="text-sm text-gray-500 dark:text-gray-400"
          >
            {data.description}
          </motion.p>
        </div>

        <div className="max-w-4xl mx-auto">
          <div className="grid lg:grid-cols-2 gap-8">
            {/* Input Section */}
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 0.4 }}
              className="bg-white dark:bg-gray-800 rounded-2xl p-8 shadow-xl"
            >
              <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-6 flex items-center gap-2">
                <RiCalculatorLine className="w-5 h-5" />
                Your App Details
              </h3>
              
              <div className="space-y-6">
                {data.inputs.map((input, index) => (
                  <div key={index}>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      {input.label}
                    </label>
                    <input
                      type="number"
                      min={input.min}
                      max={input.max}
                      value={
                        input.label.includes('users') ? users :
                        input.label.includes('price') ? price :
                        conversionRate
                      }
                      onChange={(e) => {
                        const value = parseFloat(e.target.value) || 0
                        if (input.label.includes('users')) setUsers(value)
                        else if (input.label.includes('price')) setPrice(value)
                        else setConversionRate(value)
                      }}
                      className="w-full px-4 py-3 border border-gray-200 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 dark:bg-gray-700 dark:text-white transition-all"
                      placeholder={input.placeholder}
                    />
                  </div>
                ))}
              </div>
            </motion.div>

            {/* Results Section */}
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 0.5 }}
              className="bg-gradient-to-br from-green-500 to-blue-600 rounded-2xl p-8 text-white shadow-xl"
            >
              <h3 className="text-xl font-semibold mb-6 flex items-center gap-2">
                <RiFireLine className="w-5 h-5" />
                Your Potential Revenue
              </h3>
              
              <div className="space-y-6">
                <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4">
                  <div className="text-sm opacity-90 mb-1">Monthly Revenue</div>
                  <div className="text-3xl font-bold">{formatCurrency(results.monthlyRevenue)}</div>
                </div>
                
                <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4">
                  <div className="text-sm opacity-90 mb-1">Yearly Revenue</div>
                  <div className="text-3xl font-bold">{formatCurrency(results.yearlyRevenue)}</div>
                </div>
                
                <div className="grid grid-cols-2 gap-4">
                  <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4">
                    <div className="text-sm opacity-90 mb-1">ROI Time</div>
                    <div className="text-xl font-bold">{results.roiDays} days</div>
                  </div>
                  
                  <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4">
                    <div className="text-sm opacity-90 mb-1">Success Rate</div>
                    <div className="text-xl font-bold">85%</div>
                  </div>
                </div>
                
                <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4">
                  <div className="text-sm opacity-90 mb-2">With AppSolve ($297)</div>
                  <div className="text-lg font-semibold">
                    Break even in {results.roiDays} days
                  </div>
                </div>
              </div>
            </motion.div>
          </div>
          
          {/* CTA Section */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.6 }}
            className="text-center mt-12"
          >
            <div className="bg-white dark:bg-gray-800 rounded-2xl p-8 shadow-xl max-w-2xl mx-auto">
              <p className="text-gray-600 dark:text-gray-300 mb-6">
                Ready to turn your app idea into revenue?
              </p>
              
              <a
                href={data.cta.url}
                className="inline-flex items-center gap-2 bg-green-600 hover:bg-green-700 text-white px-8 py-4 rounded-lg font-semibold transition-all transform hover:scale-105"
              >
                {data.cta.title}
                <RiArrowRightLine className="w-5 h-5" />
              </a>
              
              <p className="text-sm text-gray-500 dark:text-gray-400 mt-4">
                Based on real AppSolve success stories • 30-day money-back guarantee
              </p>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  )
}