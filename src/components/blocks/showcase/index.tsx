'use client'

import React from 'react'
import { motion } from 'framer-motion'
import { RiStarFill, RiExternalLinkLine, RiTimeLine, RiMoneyDollarCircleLine, RiTrophyLine } from 'react-icons/ri'
import { Card, CardContent } from "@/components/ui/card"
import Image from "next/image"
import Link from "next/link"
import { Section as SectionType } from "@/types/blocks/section"

// Enhanced showcase data type
interface ShowcaseData {
  name: string
  title: string
  description: string
  subtitle?: string
  items: Array<{
    title: string
    description: string
    category?: string
    image?: string
    stats?: string
    revenue?: string
    launch_time?: string
    url?: string
    developer?: string
  }>
  cta?: {
    title: string
    url: string
  }
}

export default function Showcase({ section }: { section: SectionType | ShowcaseData }) {
  // Check if this is the enhanced showcase data
  const isEnhanced = 'subtitle' in section && 'cta' in section

  if ('disabled' in section && section.disabled) {
    return null
  }

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'Productivity':
        return 'bg-blue-100 text-blue-600 dark:bg-blue-900/30 dark:text-blue-400'
      case 'Photo & Video':
        return 'bg-purple-100 text-purple-600 dark:bg-purple-900/30 dark:text-purple-400'
      case 'Music':
        return 'bg-green-100 text-green-600 dark:bg-green-900/30 dark:text-green-400'
      default:
        return 'bg-gray-100 text-gray-600 dark:bg-gray-900/30 dark:text-gray-400'
    }
  }

  // Enhanced showcase rendering
  if (isEnhanced) {
    const data = section as ShowcaseData
    return (
      <section className="py-20 bg-white dark:bg-gray-900">
        <div className="container mx-auto px-4">
          {/* Header */}
          <div className="text-center mb-16">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="inline-flex items-center gap-2 px-4 py-2 bg-green-100 dark:bg-green-900/30 rounded-full text-green-600 dark:text-green-400 text-sm font-medium mb-4"
            >
              <RiTrophyLine className="w-4 h-4" />
              Success Stories
            </motion.div>
            
            <motion.h2
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
              className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-4"
            >
              {data.title}
            </motion.h2>
            
            <motion.p
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="text-lg text-gray-600 dark:text-gray-300 mb-2"
            >
              {data.description}
            </motion.p>
            
            {data.subtitle && (
              <motion.p
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.3 }}
                className="text-sm text-gray-500 dark:text-gray-400"
              >
                {data.subtitle}
              </motion.p>
            )}
          </div>

          {/* Apps Grid */}
          <div className="grid md:grid-cols-2 gap-8 mb-16">
            {data.items.map((app, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className="bg-gray-50 dark:bg-gray-800 rounded-2xl p-6 hover:shadow-xl transition-all duration-300 group"
              >
                <div className="flex items-start gap-4">
                  {/* App Icon */}
                  <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center flex-shrink-0">
                    <span className="text-white text-2xl font-bold">
                      {app.title.charAt(0)}
                    </span>
                  </div>
                  
                  {/* App Details */}
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-2">
                      <h3 className="text-xl font-bold text-gray-900 dark:text-white">
                        {app.title}
                      </h3>
                      {app.category && (
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getCategoryColor(app.category)}`}>
                          {app.category}
                        </span>
                      )}
                    </div>
                    
                    <p className="text-gray-600 dark:text-gray-300 mb-4 text-sm leading-relaxed">
                      {app.description}
                    </p>
                    
                    {/* Stats */}
                    {(app.stats || app.revenue || app.launch_time) && (
                      <div className="grid grid-cols-3 gap-3 mb-4">
                        {app.stats && (
                          <div className="bg-white dark:bg-gray-700 rounded-lg p-3 text-center">
                            <RiStarFill className="w-4 h-4 text-yellow-500 mx-auto mb-1" />
                            <div className="text-xs text-gray-500 dark:text-gray-400">Achievement</div>
                            <div className="text-sm font-semibold text-gray-900 dark:text-white">
                              {app.stats}
                            </div>
                          </div>
                        )}
                        
                        {app.revenue && (
                          <div className="bg-white dark:bg-gray-700 rounded-lg p-3 text-center">
                            <RiMoneyDollarCircleLine className="w-4 h-4 text-green-500 mx-auto mb-1" />
                            <div className="text-xs text-gray-500 dark:text-gray-400">Revenue</div>
                            <div className="text-sm font-semibold text-gray-900 dark:text-white">
                              {app.revenue}
                            </div>
                          </div>
                        )}
                        
                        {app.launch_time && (
                          <div className="bg-white dark:bg-gray-700 rounded-lg p-3 text-center">
                            <RiTimeLine className="w-4 h-4 text-blue-500 mx-auto mb-1" />
                            <div className="text-xs text-gray-500 dark:text-gray-400">Launch Time</div>
                            <div className="text-sm font-semibold text-gray-900 dark:text-white">
                              {app.launch_time}
                            </div>
                          </div>
                        )}
                      </div>
                    )}
                    
                    {/* Developer & Link */}
                    <div className="flex items-center justify-between">
                      {app.developer && (
                        <div className="text-sm text-gray-500 dark:text-gray-400">
                          by {app.developer}
                        </div>
                      )}
                      {app.url && (
                        <a
                          href={app.url}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="inline-flex items-center gap-2 text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 text-sm font-medium transition-colors"
                        >
                          View App
                          <RiExternalLinkLine className="w-4 h-4" />
                        </a>
                      )}
                    </div>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>

          {/* CTA */}
          {data.cta && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.4 }}
              className="text-center"
            >
              <div className="bg-gradient-to-r from-green-500 to-blue-600 rounded-2xl p-8 text-white">
                <h3 className="text-2xl font-bold mb-4">Ready to build your success story?</h3>
                <p className="text-lg mb-6 opacity-90">
                  Join the developers who launched their apps in days, not months
                </p>
                <a
                  href={data.cta.url}
                  className="inline-flex items-center gap-2 bg-white text-green-600 px-8 py-4 rounded-lg font-semibold hover:bg-gray-100 transition-colors"
                >
                  {data.cta.title}
                </a>
              </div>
            </motion.div>
          )}
        </div>
      </section>
    )
  }

  // Original showcase rendering
  const originalSection = section as SectionType
  return (
    <section className="container py-16">
      <div className="mx-auto mb-12 text-center">
        <h2 className="mb-6 text-pretty text-3xl font-bold lg:text-4xl">
          {originalSection.title}
        </h2>
        <p className="mb-4 max-w-xl text-muted-foreground lg:max-w-none lg:text-lg">
          {originalSection.description}
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        {originalSection.items?.map((item, index) => (
          <Link key={index} href={item.url || ""} target={item.target}>
            <Card className="overflow-hidden transition-all hover:shadow-lg dark:hover:shadow-primary/10 p-0">
              <CardContent className="p-0">
                <div className="relative aspect-[16/10] w-full overflow-hidden">
                  <Image
                    src={item.image?.src || ""}
                    alt={item.image?.alt || item.title || ""}
                    fill
                    className="object-cover rounded-t-lg transition-transform duration-300 hover:scale-110"
                  />
                </div>
                <div className="p-6">
                  <h3 className="text-xl font-semibold mb-2 line-clamp-1">
                    {item.title}
                  </h3>
                  <p className="text-sm text-muted-foreground line-clamp-3">
                    {item.description}
                  </p>
                </div>
              </CardContent>
            </Card>
          </Link>
        ))}
      </div>
    </section>
  )
}
