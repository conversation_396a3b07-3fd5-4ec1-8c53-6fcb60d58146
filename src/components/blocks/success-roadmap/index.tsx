'use client'

import React from 'react'
import { motion } from 'framer-motion'
import { RiDownloadLine, RiCodeLine, RiSendPlaneLine, RiRocketLine, RiTimeLine, RiArrowRightLine } from 'react-icons/ri'

interface SuccessRoadmapProps {
  data: {
    name: string
    title: string
    description: string
    subtitle: string
    milestones: Array<{
      day: string
      title: string
      tasks: string[]
      time: string
      icon: string
      achievement?: string
    }>
  }
}

export default function SuccessRoadmap({ data }: SuccessRoadmapProps) {
  const getIcon = (iconName: string) => {
    switch (iconName) {
      case 'RiDownloadLine':
        return <RiDownloadLine className="w-6 h-6" />
      case 'RiCodeLine':
        return <RiCodeLine className="w-6 h-6" />
      case 'RiSendPlaneLine':
        return <RiSendPlaneLine className="w-6 h-6" />
      case 'RiRocketLine':
        return <RiRocketLine className="w-6 h-6" />
      default:
        return <RiRocketLine className="w-6 h-6" />
    }
  }

  return (
    <section className="py-20 bg-gradient-to-br from-purple-50 to-pink-50 dark:from-gray-900 dark:to-gray-800">
      <div className="container mx-auto px-4">
        {/* Header */}
        <div className="text-center mb-16">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="inline-flex items-center gap-2 px-4 py-2 bg-purple-100 dark:bg-purple-900/30 rounded-full text-purple-600 dark:text-purple-400 text-sm font-medium mb-4"
          >
            <RiTimeLine className="w-4 h-4" />
            Success Roadmap
          </motion.div>
          
          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.1 }}
            className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-4"
          >
            {data.title}
          </motion.h2>
          
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="text-lg text-gray-600 dark:text-gray-300 mb-2"
          >
            {data.description}
          </motion.p>
          
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
            className="text-sm text-gray-500 dark:text-gray-400"
          >
            {data.subtitle}
          </motion.p>
        </div>

        {/* Roadmap */}
        <div className="max-w-4xl mx-auto">
          <div className="relative">
            {/* Progress Line */}
            <div className="absolute left-8 top-16 bottom-0 w-0.5 bg-gradient-to-b from-purple-500 to-pink-500 hidden md:block" />
            
            <div className="space-y-12">
              {data.milestones.map((milestone, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, x: index % 2 === 0 ? -50 : 50 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  className={`flex items-center gap-8 ${
                    index % 2 === 0 ? 'md:flex-row' : 'md:flex-row-reverse'
                  }`}
                >
                  {/* Timeline Node */}
                  <div className="flex-shrink-0 relative">
                    <div className="w-16 h-16 bg-gradient-to-br from-purple-500 to-pink-500 rounded-full flex items-center justify-center text-white shadow-lg">
                      {getIcon(milestone.icon)}
                    </div>
                    <div className="absolute -top-2 -right-2 bg-white dark:bg-gray-800 border-2 border-purple-500 rounded-full px-2 py-1 text-xs font-semibold text-purple-600 dark:text-purple-400">
                      {milestone.day}
                    </div>
                  </div>

                  {/* Content */}
                  <div className="flex-1 bg-white dark:bg-gray-800 rounded-2xl p-8 shadow-xl">
                    <div className="mb-4">
                      <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                        {milestone.title}
                      </h3>
                      <div className="flex items-center gap-2 text-sm text-gray-500 dark:text-gray-400">
                        <RiTimeLine className="w-4 h-4" />
                        {milestone.time}
                      </div>
                    </div>

                    <div className="space-y-2 mb-6">
                      {milestone.tasks.map((task, taskIndex) => (
                        <div key={taskIndex} className="flex items-center gap-3">
                          <div className="w-2 h-2 bg-purple-500 rounded-full flex-shrink-0" />
                          <span className="text-gray-700 dark:text-gray-300">{task}</span>
                        </div>
                      ))}
                    </div>

                    {milestone.achievement && (
                      <div className="bg-gradient-to-r from-purple-100 to-pink-100 dark:from-purple-900/30 dark:to-pink-900/30 rounded-lg p-4 border border-purple-200 dark:border-purple-800">
                        <div className="flex items-center gap-2 text-purple-700 dark:text-purple-300 font-semibold">
                          <RiRocketLine className="w-5 h-5" />
                          {milestone.achievement}
                        </div>
                      </div>
                    )}
                  </div>
                </motion.div>
              ))}
            </div>
          </div>

          {/* CTA */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.6 }}
            className="text-center mt-16"
          >
            <div className="bg-gradient-to-r from-purple-500 to-pink-500 rounded-2xl p-8 text-white">
              <h3 className="text-2xl font-bold mb-4">Ready to start your journey?</h3>
              <p className="text-lg mb-6 opacity-90">
                Join thousands of developers who chose the fast path to success
              </p>
              <a
                href="#pricing"
                className="inline-flex items-center gap-2 bg-white text-purple-600 px-8 py-4 rounded-lg font-semibold hover:bg-gray-100 transition-colors"
              >
                🚀 Begin your roadmap
                <RiArrowRightLine className="w-5 h-5" />
              </a>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  )
}