'use client'

import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { RiTimeLine, RiFlashlightLine, RiCloseLine } from 'react-icons/ri'

interface UrgencyBannerProps {
  data: {
    name: string
    enabled: boolean
    type: string
    title: string
    message: string
    countdown_end: string
    next_price: string
    current_price: string
    savings: string
    cta: string
  }
}

export default function UrgencyBanner({ data }: UrgencyBannerProps) {
  const [timeLeft, setTimeLeft] = useState({
    days: 0,
    hours: 0,
    minutes: 0,
    seconds: 0
  })
  const [isVisible, setIsVisible] = useState(true)

  useEffect(() => {
    if (!data.enabled) return

    const timer = setInterval(() => {
      const now = new Date().getTime()
      const countdownDate = new Date(data.countdown_end).getTime()
      const distance = countdownDate - now

      if (distance > 0) {
        setTimeLeft({
          days: Math.floor(distance / (1000 * 60 * 60 * 24)),
          hours: Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60)),
          minutes: Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60)),
          seconds: Math.floor((distance % (1000 * 60)) / 1000)
        })
      } else {
        setTimeLeft({ days: 0, hours: 0, minutes: 0, seconds: 0 })
        clearInterval(timer)
      }
    }, 1000)

    return () => clearInterval(timer)
  }, [data.countdown_end, data.enabled])

  if (!data.enabled || !isVisible) return null

  return (
    <motion.div
      initial={{ opacity: 0, y: -50 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="fixed top-0 left-0 right-0 z-50 bg-gradient-to-r from-red-600 to-orange-600 text-white shadow-lg"
    >
      <div className="container mx-auto px-4 py-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2">
              <RiTimeLine className="w-5 h-5 animate-pulse" />
              <span className="font-semibold">{data.title}</span>
            </div>
            
            <div className="flex items-center gap-2">
              <span className="text-sm">{data.message}</span>
              <div className="flex items-center gap-1 bg-white/20 backdrop-blur-sm rounded-lg px-3 py-1">
                <span className="text-lg font-bold">
                  {timeLeft.days > 0 && `${timeLeft.days}d `}
                  {String(timeLeft.hours).padStart(2, '0')}:
                  {String(timeLeft.minutes).padStart(2, '0')}:
                  {String(timeLeft.seconds).padStart(2, '0')}
                </span>
              </div>
            </div>
            
            <div className="hidden md:flex items-center gap-2 text-sm">
              <span className="line-through opacity-75">{data.next_price}</span>
              <span className="font-bold">{data.current_price}</span>
              <span className="bg-green-500 text-white px-2 py-1 rounded text-xs">
                {data.savings} OFF
              </span>
            </div>
          </div>
          
          <div className="flex items-center gap-2">
            <a
              href="#pricing"
              className="bg-white text-red-600 px-4 py-2 rounded-lg font-semibold hover:bg-gray-100 transition-colors flex items-center gap-2"
            >
              <RiFlashlightLine className="w-4 h-4" />
              {data.cta}
            </a>
            
            <button
              onClick={() => setIsVisible(false)}
              className="p-2 hover:bg-white/10 rounded-lg transition-colors"
            >
              <RiCloseLine className="w-4 h-4" />
            </button>
          </div>
        </div>
      </div>
    </motion.div>
  )
}