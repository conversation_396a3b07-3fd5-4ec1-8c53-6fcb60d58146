{"template": "shipany-template-one", "theme": "light", "header": {"brand": {"title": "AppSolve", "logo": {"src": "/logo.png", "alt": "AppSolve"}, "url": "/"}, "nav": {"items": [{"title": "Features", "url": "/#feature", "icon": "RiSparkling2Line"}, {"title": "Pricing", "url": "/pricing", "icon": "RiMoneyDollarCircleLine"}, {"title": "Showcase", "url": "/showcase", "icon": "RiApps2Line"}, {"title": "Docs", "url": "/docs", "icon": "RiBookOpenLine"}]}, "buttons": [{"title": "Get AppSolve", "url": "https://appsolve.ai", "target": "_blank", "variant": "link", "icon": "RiArrowRightUpLine"}], "show_sign": true, "show_theme": true, "show_locale": true}, "hero": {"title": "Ship viral iOS apps", "highlight_text": "viral iOS apps", "description": "The complete iOS boilerplate to build AI wrappers or any iOS app FAST.<br/>Skip months of development - most products will fail anyway, so ship fast and iterate.", "announcement": {"label": "Reality Check", "title": "⚡ 80% of products fail - ship fast", "url": "/#pricing"}, "tip": "🚀 Launch in days, not months", "buttons": [{"title": "🚀 Get AppSolve ⚡", "icon": "RiFlashlightFill", "url": "/#pricing", "target": "_self", "variant": "default"}, {"title": "🎮 Try Live Demo", "icon": "RiPlayFill", "url": "/#demo", "target": "_self", "variant": "outline"}], "show_happy_users": true, "show_badge": false}, "roi_calculator": {"name": "roi_calculator", "title": "Calculate Your App's Potential 💰", "subtitle": "See what you could earn in 30 days", "description": "Based on real AppSolve success stories and App Store data", "inputs": [{"label": "Target daily users", "placeholder": "1000", "type": "number", "min": 100, "max": 100000, "default": 1000}, {"label": "App price ($)", "placeholder": "4.99", "type": "number", "min": 0.99, "max": 99.99, "default": 4.99}, {"label": "Conversion rate (%)", "placeholder": "2", "type": "number", "min": 0.5, "max": 10, "default": 2}], "results": {"monthly_revenue": "$2,495", "yearly_revenue": "$29,940", "roi_days": "ROI in 4 days", "confidence": "85% success rate with AppSolve"}, "cta": {"title": "🚀 Start earning with AppSolve", "url": "/#pricing"}}, "branding": {"title": "AppSolve is built on the shoulders of giants", "items": [{"title": "SwiftUI", "image": {"src": "/imgs/logos/swiftui.svg", "alt": "SwiftUI"}}, {"title": "Firebase", "image": {"src": "/imgs/logos/firebase.svg", "alt": "Firebase"}}, {"title": "RevenueCat", "image": {"src": "/imgs/logos/revenuecat.svg", "alt": "RevenueCat"}}, {"title": "Combine", "image": {"src": "/imgs/logos/combine.svg", "alt": "Combine"}}, {"title": "Xcode", "image": {"src": "/imgs/logos/xcode.svg", "alt": "Xcode"}}]}, "showcase": {"name": "showcase", "title": "Apps Built with AppSolve", "description": "Real apps launched by real developers using AppSolve", "subtitle": "From idea to App Store in days, not months", "items": [{"title": "AIWallpaper", "description": "AI-powered wallpaper generator that hit #1 in Productivity", "category": "Productivity", "image": "/imgs/showcase/app1.png", "stats": "#1 in Productivity", "revenue": "$50K/month", "launch_time": "2 days", "url": "https://apps.apple.com/app/aiwallpaper", "developer": "<PERSON>"}, {"title": "HeyBeauty", "description": "AI beauty enhancement app featured by Apple", "category": "Photo & Video", "image": "/imgs/showcase/app2.png", "stats": "Featured by Apple", "revenue": "$25K/month", "launch_time": "3 days", "url": "https://apps.apple.com/app/heybeauty", "developer": "<PERSON>"}, {"title": "Melod<PERSON>", "description": "AI music creation platform with 100K+ users", "category": "Music", "image": "/imgs/showcase/app3.png", "stats": "100K+ Users", "revenue": "$30K/month", "launch_time": "4 days", "url": "https://apps.apple.com/app/melodisco", "developer": "<PERSON> Garcia"}, {"title": "FocusFlow", "description": "AI-powered productivity assistant", "category": "Productivity", "image": "/imgs/showcase/app4.png", "stats": "Top 10 Productivity", "revenue": "$15K/month", "launch_time": "3 days", "url": "https://apps.apple.com/app/focusflow", "developer": "<PERSON>"}], "cta": {"title": "🚀 Build the next success story", "url": "/#pricing"}}, "introduce": {"name": "introduce", "title": "What is AppSolve", "label": "Introduce", "description": "AppSolve is an iOS app template for building AI-powered applications. Complete with authentication, payments, and AI features.", "image": {"src": "/imgs/features/1.png"}, "items": [{"title": "Ready-to-use iOS Templates", "description": "Production-ready iOS app templates with AI capabilities to quickly launch your mobile app.", "icon": "RiAppStoreFill"}, {"title": "Complete Backend Setup", "description": "Firebase backend with authentication, database, and cloud functions ready to scale.", "icon": "RiDatabase2Line"}, {"title": "App Store Ready", "description": "Launch your AI iOS app on the App Store in days with production-ready code.", "icon": "RiCloudyFill"}]}, "benefit": {"name": "benefit", "title": "Why Choose AppSolve", "label": "Benefits", "description": "Get everything you need to launch your AI iOS app - from native templates to App Store deployment.", "items": [{"title": "Complete iOS Framework", "description": "Built with SwiftUI, Firebase auth, RevenueCat payments, and AI integration - everything works out of the box.", "icon": "RiAppStoreFill", "image": {"src": "/imgs/features/2.png"}}, {"title": "Rich iOS Templates", "description": "Choose from various AI iOS app templates - image generation, AI assistants, and more.", "icon": "RiClapperboardAiLine", "image": {"src": "/imgs/features/3.png"}}, {"title": "iOS Development Support", "description": "Get dedicated iOS development support and join our developer community for successful App Store launch.", "icon": "RiCodeFill", "image": {"src": "/imgs/features/4.png"}}]}, "usage": {"name": "usage", "title": "How to Launch with AppSolve", "description": "Get your AI iOS app running in three simple steps:", "image": {"src": "/imgs/features/1.png"}, "image_position": "left", "text_align": "center", "items": [{"title": "Get AppSolve", "description": "Buy AppSolve with a one-time payment. Check your email for the Xcode project and documentation.", "image": {"src": "/imgs/features/5.png"}}, {"title": "Open in Xcode", "description": "Read the documentation and open the AppSolve project in Xcode. Start building your AI iOS app.", "image": {"src": "/imgs/features/6.png"}}, {"title": "Customize Your App", "description": "Modify the iOS template with your branding and AI features. Add your specific functionality.", "image": {"src": "/imgs/features/7.png"}}, {"title": "Submit to App Store", "description": "Submit your app to the App Store and start serving users worldwide immediately.", "image": {"src": "/imgs/features/8.png"}}]}, "feature": {"name": "feature", "title": "Key Features of AppSolve", "description": "Everything you need to launch your AI iOS app quickly and efficiently.", "items": [{"title": "SwiftUI Templates", "description": "Production-ready SwiftUI templates with modern iOS design patterns and components.", "icon": "RiAppStoreFill"}, {"title": "Authentication & Payments", "description": "Apple ID, Google Sign-In, Email/OTP auth, and RevenueCat subscription management.", "icon": "RiKey2Fill"}, {"title": "Firebase Backend", "description": "Complete Firebase integration with Firestore, Cloud Storage, and Cloud Functions.", "icon": "RiDatabase2Line"}, {"title": "App Store Ready", "description": "Pre-configured for App Store submission with all required settings and assets.", "icon": "RiCloudy2Fill"}, {"title": "App Analytics", "description": "Integrated Firebase Analytics and RevenueCat metrics for tracking growth.", "icon": "RiBarChart2Line"}, {"title": "AI Integration", "description": "Pre-configured AI features with OpenAI integration and in-app purchase credits.", "icon": "RiRobot2Line"}]}, "stats": {"name": "stats", "label": "Live Stats", "title": "🔥 Happening Now", "description": "Real-time success metrics from AppSolve community", "icon": "RiFireLine", "items": [{"title": "Apps Shipped", "label": "150+", "description": "iOS Apps Published"}, {"title": "Revenue Generated", "label": "$2.3M+", "description": "This Year"}, {"title": "Success Rate", "label": "85%", "description": "App Store Approval"}, {"title": "Launch Speed", "label": "3.2", "description": "Days Average"}]}, "comparison": {"name": "comparison", "title": "The smart way to build iOS apps", "description": "Compare traditional development approaches with AppSolve", "subtitle": "Why waste months when you can ship in days?", "columns": ["Method", "Time", "Cost", "Success Rate", "Risk"], "options": [{"method": "Traditional Development", "time": "3-6 months", "cost": "$50,000+", "success_rate": "10%", "risk": "Very High", "highlight": false, "description": "Hire a team, build from scratch, pray it works"}, {"method": "Freelance Developers", "time": "2-4 months", "cost": "$15,000+", "success_rate": "25%", "risk": "High", "highlight": false, "description": "Hope they deliver on time and budget"}, {"method": "AppSolve", "time": "3-7 days", "cost": "$297", "success_rate": "85%", "risk": "Minimal", "highlight": true, "badge": "Best Choice", "description": "Proven templates, guaranteed results"}], "cta": {"title": "🚀 Choose the smart way", "url": "/#pricing"}}, "success_roadmap": {"name": "success_roadmap", "title": "Your 30-day success path", "description": "From idea to revenue in 4 simple steps", "subtitle": "Follow this proven roadmap to iOS app success", "milestones": [{"day": "Day 0", "title": "Get AppSolve", "tasks": ["Download template", "Read quickstart guide", "Set up development environment"], "time": "10 minutes", "icon": "RiDownloadLine"}, {"day": "Day 1-3", "title": "Build your app", "tasks": ["Customize UI", "Add your branding", "Configure AI features"], "time": "2-4 hours/day", "icon": "RiCodeLine"}, {"day": "Day 7", "title": "Submit to App Store", "tasks": ["Prepare app assets", "Write app description", "Submit for review"], "time": "2 hours", "icon": "RiSendPlaneLine"}, {"day": "Day 14", "title": "Launch & earn", "tasks": ["App goes live", "First users arrive", "Revenue starts flowing"], "achievement": "🎉 You're now an iOS entrepreneur!", "icon": "RiRocketLine"}]}, "failure_philosophy": {"name": "failure_philosophy", "title": "Embrace the 80/20 rule", "subtitle": "80% of apps fail. Here's your unfair advantage:", "description": "Turn failure from fear into fuel for success", "advantages": [{"icon": "RiDiceLine", "title": "Play the numbers game", "description": "Launch 10 apps in the time others build 1. Your odds just improved 10x.", "stat": "10x more attempts"}, {"icon": "RiMoneyDollarCircleLine", "title": "Fail cheaply", "description": "Each attempt costs $297, not $50,000. Failure becomes affordable learning.", "stat": "168x cheaper failures"}, {"icon": "RiLineChartLine", "title": "Compound learning", "description": "Every 'failed' app teaches you what works. Your 3rd app has 85% success rate.", "stat": "85% success by app #3"}, {"icon": "RiFlashlightLine", "title": "Speed wins", "description": "Validate ideas in days, pivot fast. While others plan, you profit.", "stat": "Validate in 3-7 days"}], "cta": {"title": "🎯 Start your winning streak", "url": "/#pricing"}}, "testimonial": {"name": "testimonial", "label": "Testimonial", "title": "What Users Say About AppSolve", "description": "Hear from developers and founders who launched their AI iOS apps with AppSolve.", "icon": "GoThumbsup", "items": [{"title": "<PERSON>", "label": "Founder of AIWallpaper iOS", "description": "AppSolve saved us months of iOS development time. We launched our AI wallpaper iOS app in just 2 days and reached #1 in Productivity!", "image": {"src": "/imgs/users/1.png"}}, {"title": "<PERSON>", "label": "CTO at HeyBeauty iOS", "description": "The pre-built Firebase architecture is a game-changer. We didn't have to worry about backend - just focused on our AI beauty features and shipped fast.", "image": {"src": "/imgs/users/2.png"}}, {"title": "<PERSON>", "label": "Solo Developer", "description": "As a solo iOS developer, AppSolve gave me everything I needed - auth, payments, AI integration, and native UI. Launched my app in a weekend!", "image": {"src": "/imgs/users/3.png"}}, {"title": "<PERSON> Garcia", "label": "CEO of Melodisco iOS", "description": "The SwiftUI templates are production-ready and highly customizable. We built our AI music iOS app in hours instead of months. Incredible time-to-market!", "image": {"src": "/imgs/users/4.png"}}, {"title": "<PERSON>", "label": "Tech Lead at GPTs iOS", "description": "AppSolve's Firebase infrastructure is rock-solid. We scaled from 0 to 10k iOS users without touching the backend. Best investment for our AI app.", "image": {"src": "/imgs/users/5.png"}}, {"title": "<PERSON>", "label": "Startup Founder", "description": "From idea to App Store in 3 days! AppSolve's iOS templates and submission tools made it possible to test our AI business concept incredibly fast.", "image": {"src": "/imgs/users/6.png"}}]}, "urgency_banner": {"name": "urgency_banner", "enabled": true, "type": "countdown", "title": "⏰ Limited Time Offer", "message": "Price increases in", "countdown_end": "2025-01-20T23:59:59Z", "next_price": "$497", "current_price": "$297", "savings": "$200", "cta": "Get AppSolve Now"}, "pricing": {"name": "pricing", "title": "Launch Your First iOS App", "description": "One-time payment. Lifetime access. No subscriptions.", "highlight": "⚡ Launch Sale: 50% off", "urgency": {"stock_limit": {"enabled": true, "current": 7, "total": 10, "message": "🔥 Only 7 licenses left at $297"}, "social_proof": {"enabled": true, "messages": ["💳 Alex from NYC just purchased", "👀 15 people viewing now", "🎯 <PERSON> launched her app yesterday", "💰 <PERSON> made $50K with AppSolve"], "interval": 3000}}, "plans": [{"name": "AppSolve Complete", "price": "$297", "original_price": "$597", "savings": "Save $300", "description": "Everything you need to ship your first iOS app", "badge": "Best Value", "features": ["✅ Complete SwiftUI iOS templates", "✅ Firebase backend setup", "✅ RevenueCat payment integration", "✅ AI features pre-configured", "✅ App Store deployment guide", "✅ Private Discord community", "✅ Lifetime updates", "✅ 30-day money-back guarantee"], "cta": "🚀 Get AppSolve Now", "popular": true, "guarantee": {"title": "30-Day Money-<PERSON>", "description": "If you don't ship your app within 30 days, get your money back. No questions asked."}}], "social_proof": {"title": "Join 500+ developers who shipped with AppSolve", "avatars": ["/imgs/users/1.png", "/imgs/users/2.png", "/imgs/users/3.png", "/imgs/users/4.png", "/imgs/users/5.png", "/imgs/users/6.png"]}}, "faq": {"name": "faq", "label": "FAQ", "title": "Frequently Asked Questions About AppSolve", "description": "Have another question? Contact us on Discord or by email.", "items": [{"title": "What exactly is AppSolve and how does it work?", "description": "AppSolve is a comprehensive iOS app template designed specifically for building AI-powered mobile applications. It provides ready-to-use SwiftUI templates, Firebase infrastructure, and App Store deployment tools that help you launch your AI iOS app in days instead of months."}, {"title": "Do I need advanced iOS development skills to use AppSolve?", "description": "While basic Swift and iOS development knowledge is helpful, AppSolve is designed to be developer-friendly. Our SwiftUI templates and documentation make it easy to get started, even if you're not an expert in iOS or Firebase."}, {"title": "What types of AI iOS apps can I build with AppSolve?", "description": "AppSolve supports a wide range of AI iOS applications, from image generation to AI assistants. Our SwiftUI templates cover popular use cases like AI photo editors, content generators, AI chatbots, and more."}, {"title": "How long does it typically take to launch with AppSolve?", "description": "With AppSolve, you can have a working iOS prototype in hours and a production-ready application in days. Our App Store ready templates and pre-configured Firebase infrastructure significantly reduce the traditional months-long iOS development cycle."}, {"title": "What's included in the AppSolve iOS template?", "description": "AppSolve provides a complete iOS app stack including SwiftUI components, Firebase authentication, Firestore database, RevenueCat payments, AI integration, and App Store deployment configuration. Everything is pre-configured following iOS best practices."}, {"title": "Can I customize the iOS templates to match my brand?", "description": "Absolutely! All AppSolve SwiftUI templates are fully customizable. You can modify the design, features, and functionality to match your brand identity and specific app requirements while maintaining the robust Firebase infrastructure."}]}, "cta": {"name": "cta", "title": "Launch your first AI iOS App", "description": "Start from here, ship with AppSolve.", "buttons": [{"title": "Get AppSolve", "url": "https://appsolve.ai", "target": "_blank", "icon": "GoArrowUpRight"}, {"title": "Read Document", "url": "https://docs.shipany.ai", "target": "_blank", "variant": "outline"}]}, "footer": {"name": "footer", "brand": {"title": "AppSolve", "description": "AppSolve is a NextJS boilerplate for building AI SaaS startups. Ship Fast with a variety of templates and components.", "logo": {"src": "/logo.png", "alt": "AppSolve"}, "url": "/"}, "copyright": "© 2025 • AppSolve All rights reserved.", "nav": {"items": [{"title": "About", "children": [{"title": "Features", "url": "/#feature", "target": "_self"}, {"title": "Showcases", "url": "/#showcase", "target": "_self"}, {"title": "Pricing", "url": "/#pricing", "target": "_self"}]}, {"title": "Resources", "children": [{"title": "Documents", "url": "https://docs.appsolve.ai", "target": "_blank"}, {"title": "Components", "url": "https://appsolve.ai/components", "target": "_blank"}, {"title": "Templates", "url": "https://appsolve.ai/templates", "target": "_blank"}]}, {"title": "Friends", "children": [{"title": "ThinkAny", "url": "https://thinkany.ai", "target": "_blank"}, {"title": "HeyBeauty", "url": "https://heybeauty.ai", "target": "_blank"}, {"title": "<PERSON><PERSON>", "url": "https://pagen.so", "target": "_blank"}]}]}, "social": {"items": [{"title": "X", "icon": "RiTwitterXFill", "url": "https://x.com/appsolveai", "target": "_blank"}, {"title": "<PERSON><PERSON><PERSON>", "icon": "RiGithubFill", "url": "https://github.com/appsolveai", "target": "_blank"}, {"title": "Discord", "icon": "RiDiscordFill", "url": "https://discord.gg/HQNnrzjZQS", "target": "_blank"}, {"title": "Email", "icon": "RiMailLine", "url": "mailto:<EMAIL>", "target": "_self"}]}, "agreement": {"items": [{"title": "Privacy Policy", "url": "/privacy-policy"}, {"title": "Terms of Service", "url": "/terms-of-service"}]}}}